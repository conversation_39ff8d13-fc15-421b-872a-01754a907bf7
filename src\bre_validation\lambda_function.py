import os
import json

from validation_helper.validation_execution import ValidatorExecution
from common.aria_helper import CrudStatuses
from common.aria_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from common.aria_helper.boto3_utils import get_secret
from common import Mongo

environment = os.environ['ENV']
database_name = os.environ['DATABASE_NAME']
aria_environment = os.environ['ARIA_ENVIRONMENT']


class BreValidationHandler:
    def __init__(self, event):
        self.event = event
        self.input_body = json.loads(event['body'])
        self.action_data = self.input_body['action']
        self.document = self.input_body['document']
        self.app_id = self.document['app_id']
        self.statuses = self.input_body['status']
        self.mongo_client = Mongo(get_secret(environment + '-mongodb_uri', return_json=False)).client
        self.aria_secret = get_secret(secret_name=f'{environment}-aria_cm_tokens')
        self.crud_handler = <PERSON>rudHandler(self.mongo_client)
        self.crud_statuses = CrudStatuses(self.mongo_client)
        self.validator_execution = ValidatorExecution()
        self.validation_config = self.mongo_client[database_name]["validation_config"].find_one({"app_id": self.app_id})

    def run(self):
        try:
            # Perform validation first
            is_valid, validation_result = self.validator_execution.validate_data(self.document, self.validation_config)

            # Save on db what we selected on this run
            execution_id = self.crud_handler.insert_execution(self.input_body, next_function)

            # Update the information on the input_body
            self.input_body['bre_type'] = bre_type
            self.input_body['execution_id'] = execution_id
            self.input_body['request_response'] = request_response
            self.input_body['validation_result'] = validation_result

            return {
                'statusCode': 200,
                'body': json.dumps({'message': 'Working on it'})
            }

        except Exception as e:
            self.crud_handler.insert_execution(self.event, 'None', failed=True, error_message=str(e))
            return {
                'statusCode': 500,
                'body': {'message': 'Issue while processing the petition: ' + str(e)}
            }


def lambda_handler(event, context):
    print(event)
    if 'body' not in list(event.keys()):
        raise ValueError("body tag is missing on the dict. Skipping...")

    bre_validation_handler = BreValidationHandler(event)
    return bre_validation_handler.run()